<?= $this->extend('templates/print_template') ?>

<?= $this->section('styles') ?>
<style>
    /* Output-specific color theme */
    .evaluation-header {
        border-bottom-color: #198754;
    }
    
    .evaluation-header h1 {
        color: #198754;
    }
    
    .section-title {
        color: #198754;
        border-bottom-color: #198754;
    }
    
    .implementation-item {
        border-left-color: #198754;
    }
    
    .proposal-header h4 {
        color: #198754;
    }
    
    .implementation-item h6 {
        color: #198754;
    }
    
    .content-box h6 {
        color: #198754;
    }
    
    .metric-card h4 {
        color: #198754;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<!-- Professional Header for Print -->
<div class="evaluation-header">
    <h1>Output Activity Evaluation Report</h1>
    <h2><?= esc($activity['title']) ?></h2>
    <p>Activity Code: <strong><?= esc($activity['activity_code'] ?? 'N/A') ?></strong></p>
    <p>Generated on: <strong><?= date('F d, Y') ?></strong></p>
</div>

<!-- Activity Information -->
<div class="info-section">
    <h3 class="section-title">Activity Information</h3>
    <div class="row">
        <!-- Basic Information -->
        <div class="col-md-6">
            <table class="info-table">
                <tr>
                    <th>Activity Code</th>
                    <td><strong><?= esc($activity['activity_code'] ?? 'N/A') ?></strong></td>
                </tr>
                <tr>
                    <th>Output Title</th>
                    <td><strong><?= esc($activity['title']) ?></strong></td>
                </tr>
                <tr>
                    <th>Activity Type</th>
                    <td>Output</td>
                </tr>
                <tr>
                    <th>Workplan</th>
                    <td>
                        <strong><?= esc($workplan['title'] ?? 'N/A') ?></strong>
                        <?php if (!empty($workplan['start_date'])): ?>
                            <br><small style="color: #6c757d;">
                                <?= date('Y', strtotime($workplan['start_date'])) ?>
                                <?php if (!empty($workplan['end_date']) && date('Y', strtotime($workplan['start_date'])) != date('Y', strtotime($workplan['end_date']))): ?>
                                    - <?= date('Y', strtotime($workplan['end_date'])) ?>
                                <?php endif; ?>
                            </small>
                        <?php endif; ?>
                    </td>
                </tr>
                <tr>
                    <th>Branch</th>
                    <td><?= esc($branch['name'] ?? 'N/A') ?></td>
                </tr>
                <tr>
                    <th>Supervisor</th>
                    <td><?= esc($activity['supervisor_name'] ?? 'Not assigned') ?></td>
                </tr>
                <tr>
                    <th>Total Budget</th>
                    <td><strong><?= CURRENCY_SYMBOL ?> <?= $activity['total_budget'] ? number_format($activity['total_budget'], 2) : '0.00' ?></strong></td>
                </tr>
            </table>
        </div>
        
        <!-- Performance & Evaluation -->
        <div class="col-md-6">
            <table class="info-table">
                <tr>
                    <th colspan="2" style="text-align: center; background-color: #d1e7dd;">Quarterly Performance</th>
                </tr>
                <tr>
                    <th>Quarter</th>
                    <th>Target / Achieved / Percentage</th>
                </tr>
                <tr>
                    <td><strong>Q1</strong></td>
                    <td>
                        <?php
                        $q1_target = $activity['q_one_target'] ? number_format($activity['q_one_target'], 0) : 'N/A';
                        $q1_achieved = $activity['q_one_achieved'] ? number_format($activity['q_one_achieved'], 0) : 'N/A';
                        $q1_percent = ($activity['q_one_target'] && $activity['q_one_achieved'])
                            ? round(($activity['q_one_achieved'] / $activity['q_one_target']) * 100, 1)
                            : 0;
                        ?>
                        <?= $q1_target ?> / <?= $q1_achieved ?> / <strong><?= $q1_percent ?>%</strong>
                    </td>
                </tr>
                <tr>
                    <td><strong>Q2</strong></td>
                    <td>
                        <?php
                        $q2_target = $activity['q_two_target'] ? number_format($activity['q_two_target'], 0) : 'N/A';
                        $q2_achieved = $activity['q_two_achieved'] ? number_format($activity['q_two_achieved'], 0) : 'N/A';
                        $q2_percent = ($activity['q_two_target'] && $activity['q_two_achieved'])
                            ? round(($activity['q_two_achieved'] / $activity['q_two_target']) * 100, 1)
                            : 0;
                        ?>
                        <?= $q2_target ?> / <?= $q2_achieved ?> / <strong><?= $q2_percent ?>%</strong>
                    </td>
                </tr>
                <tr>
                    <td><strong>Q3</strong></td>
                    <td>
                        <?php
                        $q3_target = $activity['q_three_target'] ? number_format($activity['q_three_target'], 0) : 'N/A';
                        $q3_achieved = $activity['q_three_achieved'] ? number_format($activity['q_three_achieved'], 0) : 'N/A';
                        $q3_percent = ($activity['q_three_target'] && $activity['q_three_achieved'])
                            ? round(($activity['q_three_achieved'] / $activity['q_three_target']) * 100, 1)
                            : 0;
                        ?>
                        <?= $q3_target ?> / <?= $q3_achieved ?> / <strong><?= $q3_percent ?>%</strong>
                    </td>
                </tr>
                <tr>
                    <td><strong>Q4</strong></td>
                    <td>
                        <?php
                        $q4_target = $activity['q_four_target'] ? number_format($activity['q_four_target'], 0) : 'N/A';
                        $q4_achieved = $activity['q_four_achieved'] ? number_format($activity['q_four_achieved'], 0) : 'N/A';
                        $q4_percent = ($activity['q_four_target'] && $activity['q_four_achieved'])
                            ? round(($activity['q_four_achieved'] / $activity['q_four_target']) * 100, 1)
                            : 0;
                        ?>
                        <?= $q4_target ?> / <?= $q4_achieved ?> / <strong><?= $q4_percent ?>%</strong>
                    </td>
                </tr>
                <tr style="background-color: #f8f9fa;">
                    <td><strong>TOTAL</strong></td>
                    <td>
                        <?php
                        $total_target = ($activity['q_one_target'] ?? 0) + ($activity['q_two_target'] ?? 0) +
                                       ($activity['q_three_target'] ?? 0) + ($activity['q_four_target'] ?? 0);
                        $total_achieved = ($activity['q_one_achieved'] ?? 0) + ($activity['q_two_achieved'] ?? 0) +
                                         ($activity['q_three_achieved'] ?? 0) + ($activity['q_four_achieved'] ?? 0);
                        $total_percent = ($total_target && $total_achieved)
                            ? round(($total_achieved / $total_target) * 100, 1)
                            : 0;
                        ?>
                        <strong><?= $total_target ? number_format($total_target, 0) : 'N/A' ?> / 
                        <?= $total_achieved ? number_format($total_achieved, 0) : 'N/A' ?> / 
                        <?= $total_percent ?>%</strong>
                    </td>
                </tr>
                <?php if (!empty($activity['rating']) && $activity['rating'] > 0): ?>
                <tr>
                    <th>Activity Rating</th>
                    <td>
                        <strong><?= $activity['rating'] ?>/5</strong>
                        <?php if (!empty($activity['rated_at'])): ?>
                            <br><small style="color: #6c757d;">Rated on <?= date('M d, Y', strtotime($activity['rated_at'])) ?></small>
                        <?php endif; ?>
                    </td>
                </tr>
                <?php if (!empty($activity['reated_remarks'])): ?>
                <tr>
                    <th>Evaluation Remarks</th>
                    <td><?= nl2br(esc($activity['reated_remarks'])) ?></td>
                </tr>
                <?php endif; ?>
                <?php else: ?>
                <tr>
                    <th>Activity Rating</th>
                    <td><em>Not yet rated</em></td>
                </tr>
                <?php endif; ?>
            </table>
        </div>
    </div>
</div>

<!-- Summary Statistics -->
<div class="info-section">
    <h3 class="section-title">Summary Statistics</h3>
    <div class="performance-grid">
        <div class="metric-card">
            <h4><?= CURRENCY_SYMBOL ?> <?= $activity['total_budget'] ? number_format($activity['total_budget'], 2) : '0.00' ?></h4>
            <p>Output Budget</p>
        </div>
        <div class="metric-card">
            <h4><?= $proposalSummary['total'] ?></h4>
            <p>Total Proposals</p>
        </div>
        <div class="metric-card">
            <h4><?= $proposalSummary['approved'] ?></h4>
            <p>Approved Proposals</p>
        </div>
        <div class="metric-card">
            <h4><?= count($proposals) ?></h4>
            <p>For Evaluation</p>
        </div>
    </div>
</div>

<!-- Description -->
<?php if (!empty($activity['description'])): ?>
<div class="info-section">
    <h3 class="section-title">Output Description</h3>
    <div class="content-box">
        <?= nl2br(esc($activity['description'])) ?>
    </div>
</div>
<?php endif; ?>

<!-- Output Proposals and Implementation Details -->
<div class="info-section page-break">
    <h3 class="section-title">Output Proposals - Implementation Details</h3>
    <p style="color: #6c757d; margin-bottom: 30px;">Approved or rated output proposals with full implementation details</p>
    
    <?php if (empty($proposals)): ?>
        <div style="padding: 20px; background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; color: #856404;">
            <strong>Notice:</strong> No approved or rated output proposals found for this activity.
        </div>
    <?php else: ?>
        <?php foreach ($proposals as $index => $proposal): ?>
            <div class="proposal-section">
                <!-- Proposal Header -->
                <div class="proposal-header">
                    <h4>
                        Output Proposal #<?= $index + 1 ?>
                        <span style="font-size: 0.8em; padding: 4px 8px; border: 1px solid #198754; border-radius: 4px;">
                            <?= ucfirst(esc($proposal['status'] ?? 'N/A')) ?>
                        </span>
                    </h4>
                    <p style="margin: 5px 0 0 0; color: #6c757d;">Created: <?= date('M d, Y', strtotime($proposal['created_at'])) ?></p>
                </div>

                <!-- Proposal Details -->
                <div style="padding: 20px;">
                    <div class="row">
                        <div class="col-md-6">
                            <h5 style="color: #198754; margin-bottom: 15px;">Output Information</h5>
                            <table class="info-table">
                                <tr>
                                    <th>Production Period</th>
                                    <td>
                                        <?= $proposal['date_start'] ? date('M d, Y', strtotime($proposal['date_start'])) : 'N/A' ?>
                                        <?php if ($proposal['date_end']): ?>
                                            - <?= date('M d, Y', strtotime($proposal['date_end'])) ?>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <tr>
                                    <th>Location</th>
                                    <td><?= esc($proposal['location'] ?? 'N/A') ?></td>
                                </tr>
                                <tr>
                                    <th>Province</th>
                                    <td><?= esc($proposal['province_name'] ?? 'N/A') ?></td>
                                </tr>
                                <tr>
                                    <th>District</th>
                                    <td><?= esc($proposal['district_name'] ?? 'N/A') ?></td>
                                </tr>
                                <tr>
                                    <th>Total Cost</th>
                                    <td><strong><?= CURRENCY_SYMBOL ?> <?= $proposal['total_cost'] ? number_format($proposal['total_cost'], 2) : '0.00' ?></strong></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h5 style="color: #198754; margin-bottom: 15px;">Personnel & Rating</h5>
                            <table class="info-table">
                                <tr>
                                    <th>Supervisor</th>
                                    <td><?= esc($proposal['supervisor_name'] ?? 'Not assigned') ?></td>
                                </tr>
                                <tr>
                                    <th>Action Officer</th>
                                    <td><?= esc($proposal['action_officer_name'] ?? 'Not assigned') ?></td>
                                </tr>
                                <?php if (!empty($proposal['rating_score'])): ?>
                                <tr>
                                    <th>Rating</th>
                                    <td>
                                        <strong><?= esc($proposal['rating_score']) ?>/10</strong>
                                        <?php if ($proposal['rated_at']): ?>
                                            <br><small style="color: #6c757d;">Rated: <?= date('M d, Y', strtotime($proposal['rated_at'])) ?></small>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <?php endif; ?>
                                <?php if (!empty($proposal['rate_remarks'])): ?>
                                <tr>
                                    <th>Rating Remarks</th>
                                    <td><?= esc($proposal['rate_remarks']) ?></td>
                                </tr>
                                <?php endif; ?>
                            </table>
                        </div>
                    </div>
                </div>

                <?php if (!empty($proposal['implementations'])): ?>
                    <div style="margin-top: 30px;">
                        <h5 style="color: #198754; margin-bottom: 20px; padding: 0 20px;">
                            Output Implementation Details
                            <span style="font-size: 0.8em; padding: 4px 8px; border: 1px solid #198754; border-radius: 4px;">
                                <?= count($proposal['implementations']) ?> Records
                            </span>
                        </h5>

                        <?php foreach ($proposal['implementations'] as $impl_index => $implementation): ?>
                            <?php if ($implementation['type'] === 'Output'): ?>
                                <div class="implementation-item">
                                    <h6>
                                        Output Production #<?= $impl_index + 1 ?>
                                        <span style="float: right; color: #6c757d; font-size: 0.9em;">
                                            <?= date('M d, Y', strtotime($implementation['created_at'])) ?>
                                        </span>
                                    </h6>

                                    <div class="row">
                                        <div class="col-md-8">
                                            <?php if (!empty($implementation['outputs_produced'])): ?>
                                                <div class="content-box">
                                                    <h6>Outputs Produced</h6>
                                                    <?= nl2br(esc($implementation['outputs_produced'])) ?>
                                                </div>
                                            <?php endif; ?>

                                            <?php if (!empty($implementation['production_details'])): ?>
                                                <div class="content-box">
                                                    <h6>Production Details</h6>
                                                    <?= nl2br(esc($implementation['production_details'])) ?>
                                                </div>
                                            <?php endif; ?>

                                            <?php if (!empty($implementation['quality_measures'])): ?>
                                                <div class="content-box">
                                                    <h6>Quality Measures</h6>
                                                    <?= nl2br(esc($implementation['quality_measures'])) ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>

                                        <div class="col-md-4">
                                            <div class="content-box">
                                                <h6>Production Information</h6>

                                                <?php if (!empty($implementation['gps_coordinates'])): ?>
                                                    <p style="margin-bottom: 10px;"><strong>GPS Location:</strong><br>
                                                    <small style="font-family: monospace; background-color: #fff; padding: 4px; border-radius: 4px; border: 1px solid #dee2e6;"><?= esc($implementation['gps_coordinates']) ?></small></p>
                                                <?php endif; ?>

                                                <p style="margin-bottom: 10px;"><strong>Production Date:</strong><br>
                                                <small><?= date('M d, Y H:i', strtotime($implementation['created_at'])) ?></small></p>

                                                <?php if (!empty($implementation['quantity_produced'])): ?>
                                                    <p style="margin-bottom: 10px;"><strong>Quantity Produced:</strong><br>
                                                    <strong><?= esc($implementation['quantity_produced']) ?></strong></p>
                                                <?php endif; ?>

                                                <?php if (!empty($implementation['production_cost'])): ?>
                                                    <p style="margin-bottom: 10px;"><strong>Production Cost:</strong><br>
                                                    <strong><?= CURRENCY_SYMBOL ?> <?= number_format($implementation['production_cost'], 2) ?></strong></p>
                                                <?php endif; ?>
                                            </div>

                                            <!-- Production Images -->
                                            <?php if (!empty($implementation['production_images'])): ?>
                                                <div class="content-box">
                                                    <h6>Production Photos</h6>
                                                    <?php
                                                    $images = is_string($implementation['production_images']) ? json_decode($implementation['production_images'], true) : $implementation['production_images'];
                                                    if (is_array($images) && !empty($images)) {
                                                        echo '<p><strong>' . count($images) . ' photo(s) available</strong></p>';
                                                        echo '<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(80px, 1fr)); gap: 5px;">';
                                                        foreach (array_slice($images, 0, 4) as $image) {
                                                            $imagePath = is_array($image) ? ($image['path'] ?? $image['url'] ?? '') : $image;
                                                            if (!empty($imagePath)) {
                                                                echo '<div>';
                                                                echo '<img src="' . base_url($imagePath) . '" style="width: 100%; max-height: 60px; object-fit: cover; border-radius: 4px; border: 1px solid #dee2e6;">';
                                                                echo '</div>';
                                                            }
                                                        }
                                                        echo '</div>';
                                                    }
                                                    ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>

                                    <?php if (!empty($implementation['remarks'])): ?>
                                        <div style="margin-top: 20px;">
                                            <div class="content-box">
                                                <h6>Production Remarks</h6>
                                                <?= nl2br(esc($implementation['remarks'])) ?>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <div style="padding: 20px; background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; color: #856404; margin: 20px;">
                        <strong>Notice:</strong> No output implementation activities recorded for this proposal yet.
                    </div>
                <?php endif; ?>

                <?php if ($index < count($proposals) - 1): ?>
                    <div class="section-divider"></div>
                <?php endif; ?>
            </div>
        <?php endforeach; ?>
    <?php endif; ?>
</div>
<?= $this->endSection() ?>
